
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>my-ls: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">github.com/jesee-kuya/my-ls/main.go (100.0%)</option>
				
				<option value="file1">github.com/jesee-kuya/my-ls/print/print.go (91.9%)</option>
				
				<option value="file2">github.com/jesee-kuya/my-ls/util/hasAnsi.go (100.0%)</option>
				
				<option value="file3">github.com/jesee-kuya/my-ls/util/isValidDir.go (100.0%)</option>
				
				<option value="file4">github.com/jesee-kuya/my-ls/util/readDir.go (88.9%)</option>
				
				<option value="file5">github.com/jesee-kuya/my-ls/util/reverse.go (100.0%)</option>
				
				<option value="file6">github.com/jesee-kuya/my-ls/util/sorted.go (92.7%)</option>
				
				<option value="file7">github.com/jesee-kuya/my-ls/util/stripAnsi.go (100.0%)</option>
				
				<option value="file8">github.com/jesee-kuya/my-ls/util/time.go (100.0%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package main

import (
        "os"

        "github.com/jesee-kuya/my-ls/print"
        "github.com/jesee-kuya/my-ls/util"
)

// parseArgs parses command-line arguments and returns flags and paths
func parseArgs(args []string) (util.Flags, []string) <span class="cov8" title="1">{
        flags := util.Flags{}
        var paths []string

        for _, arg := range args </span><span class="cov8" title="1">{
                if len(arg) &gt; 0 &amp;&amp; arg[0] == '-' </span><span class="cov8" title="1">{
                        // Parse flags
                        for _, char := range arg[1:] </span><span class="cov8" title="1">{
                                switch char </span>{
                                case 'a':<span class="cov8" title="1">
                                        flags.ShowAll = true</span>
                                case 'l':<span class="cov8" title="1">
                                        flags.Longformat = true</span>
                                case 'r':<span class="cov8" title="1">
                                        flags.Reverse = true</span>
                                case 'R':<span class="cov8" title="1">
                                        flags.Recursive = true</span>
                                case 't':<span class="cov8" title="1">
                                        flags.TimeSort = true</span>
                                }
                        }
                } else<span class="cov8" title="1"> {
                        // It's a path
                        paths = append(paths, arg)
                }</span>
        }

        // If no paths specified, use current directory
        <span class="cov8" title="1">if len(paths) == 0 </span><span class="cov8" title="1">{
                paths = []string{"."}
        }</span>

        <span class="cov8" title="1">return flags, paths</span>
}

func main() <span class="cov8" title="1">{
        var flags util.Flags
        var paths []string

        if len(os.Args) &gt; 1 </span><span class="cov8" title="1">{
                flags, paths = parseArgs(os.Args[1:])
        }</span> else<span class="cov8" title="1"> {
                paths = []string{"."}
        }</span>

        <span class="cov8" title="1">print.Print(paths, flags)</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package print

import (
        "fmt"
        "strings"
        "syscall"
        "unsafe"

        "github.com/jesee-kuya/my-ls/util"
)

// getTerminalWidth returns the terminal width, defaulting to 80 if unable to determine
func getTerminalWidth() int <span class="cov8" title="1">{
        type winsize struct {
                Row    uint16
                Col    uint16
                Xpixel uint16
                Ypixel uint16
        }

        ws := &amp;winsize{}
        retCode, _, _ := syscall.Syscall(syscall.SYS_IOCTL,
                uintptr(syscall.Stdin),
                uintptr(syscall.TIOCGWINSZ),
                uintptr(unsafe.Pointer(ws)))

        if int(retCode) == -1 </span><span class="cov8" title="1">{
                return 80 // Default width
        }</span>
        <span class="cov0" title="0">return int(ws.Col)</span>
}

// formatInColumns formats a list of files in columns like standard ls
func formatInColumns(files []string) string <span class="cov8" title="1">{
        if len(files) == 0 </span><span class="cov8" title="1">{
                return ""
        }</span>

        // Strip ANSI codes to calculate actual display width
        <span class="cov8" title="1">displayFiles := make([]string, len(files))
        fileLengths := make([]int, len(files))
        maxLen := 0
        for i, file := range files </span><span class="cov8" title="1">{
                displayFiles[i] = util.StripANSI(file)
                fileLengths[i] = len(displayFiles[i])
                if fileLengths[i] &gt; maxLen </span><span class="cov8" title="1">{
                        maxLen = fileLengths[i]
                }</span>
        }

        <span class="cov8" title="1">termWidth := getTerminalWidth()

        // Try different numbers of columns to find the optimal layout
        bestCols := 1
        bestRows := len(files)

        for numCols := 1; numCols &lt;= len(files); numCols++ </span><span class="cov8" title="1">{
                numRows := (len(files) + numCols - 1) / numCols

                // Calculate column widths for this layout (column-major ordering)
                colWidths := make([]int, numCols)
                for col := 0; col &lt; numCols; col++ </span><span class="cov8" title="1">{
                        maxColWidth := 0
                        for row := 0; row &lt; numRows; row++ </span><span class="cov8" title="1">{
                                idx := col*numRows + row // Column-major indexing
                                if idx &lt; len(files) </span><span class="cov8" title="1">{
                                        if fileLengths[idx] &gt; maxColWidth </span><span class="cov8" title="1">{
                                                maxColWidth = fileLengths[idx]
                                        }</span>
                                }
                        }
                        <span class="cov8" title="1">colWidths[col] = maxColWidth</span>
                }

                // Calculate total width needed
                <span class="cov8" title="1">totalWidth := 0
                for i, width := range colWidths </span><span class="cov8" title="1">{
                        totalWidth += width
                        if i &lt; len(colWidths)-1 </span><span class="cov8" title="1">{
                                totalWidth += 2 // Space between columns
                        }</span>
                }

                // If this layout fits and uses fewer rows, use it
                <span class="cov8" title="1">if totalWidth &lt;= termWidth &amp;&amp; numRows &lt; bestRows </span><span class="cov8" title="1">{
                        bestCols = numCols
                        bestRows = numRows
                }</span>
        }

        // Format using the best layout
        <span class="cov8" title="1">numRows := (len(files) + bestCols - 1) / bestCols

        // Calculate column widths for the best layout (column-major ordering)
        colWidths := make([]int, bestCols)
        for col := 0; col &lt; bestCols; col++ </span><span class="cov8" title="1">{
                maxColWidth := 0
                for row := 0; row &lt; numRows; row++ </span><span class="cov8" title="1">{
                        idx := col*numRows + row // Column-major indexing
                        if idx &lt; len(files) </span><span class="cov8" title="1">{
                                if fileLengths[idx] &gt; maxColWidth </span><span class="cov8" title="1">{
                                        maxColWidth = fileLengths[idx]
                                }</span>
                        }
                }
                <span class="cov8" title="1">colWidths[col] = maxColWidth</span>
        }

        <span class="cov8" title="1">var result strings.Builder
        for row := 0; row &lt; numRows; row++ </span><span class="cov8" title="1">{
                for col := 0; col &lt; bestCols; col++ </span><span class="cov8" title="1">{
                        idx := col*numRows + row // Column-major indexing
                        if idx &lt; len(files) </span><span class="cov8" title="1">{
                                result.WriteString(files[idx])

                                // Add padding if not the last column in the row
                                if col &lt; bestCols-1 </span><span class="cov8" title="1">{
                                        padding := colWidths[col] - fileLengths[idx] + 2
                                        if padding &lt; 2 </span><span class="cov0" title="0">{
                                                padding = 2 // Minimum 2 spaces
                                        }</span>
                                        <span class="cov8" title="1">result.WriteString(strings.Repeat(" ", padding))</span>
                                }
                        }
                }
                <span class="cov8" title="1">if row &lt; numRows-1 </span><span class="cov0" title="0">{
                        result.WriteString("\n")
                }</span>
        }

        <span class="cov8" title="1">return result.String()</span>
}

func Print(paths []string, flags util.Flags) <span class="cov8" title="1">{
        outErrors := []string{}
        singleFiles := []string{}
        dirContents := []string{}
        content := []any{}

        // Handle recursive listing
        if flags.Recursive </span><span class="cov8" title="1">{
                allPaths, err := util.CollectDirectoriesRecursively(paths, flags)
                if err != nil </span><span class="cov0" title="0">{
                        outErrors = append(outErrors, fmt.Sprintf("Error during recursive traversal: %v\n", err.Error()))
                }</span> else<span class="cov8" title="1"> {
                        paths = allPaths
                }</span>
        }

        <span class="cov8" title="1">multipleDirs := false
        if len(paths) &gt; 1 || flags.Recursive </span><span class="cov8" title="1">{
                multipleDirs = true
        }</span>

        <span class="cov8" title="1">for _, dirPath := range paths </span><span class="cov8" title="1">{
                info, err := util.IsValidDir(dirPath)
                if err != nil </span><span class="cov8" title="1">{
                        outErrors = append(outErrors, fmt.Sprintf("Error: %v\n", err.Error()))
                        continue</span>
                }

                <span class="cov8" title="1">if !info.IsDir() </span><span class="cov8" title="1">{
                        singleFiles = append(singleFiles, dirPath)
                        continue</span>
                }
                <span class="cov8" title="1">var files []string

                if flags.Longformat </span><span class="cov8" title="1">{
                        files, err = util.ReadDirNamesLong(dirPath, flags)
                        if err != nil </span><span class="cov0" title="0">{
                                outErrors = append(outErrors, fmt.Sprintf("Error reading directory: %v\n", err.Error()))
                                continue</span>
                        }
                } else<span class="cov8" title="1"> {
                        files, err = util.ReadDirNames(dirPath, flags)
                        if err != nil </span><span class="cov0" title="0">{
                                outErrors = append(outErrors, fmt.Sprintf("Error reading directory: %v\n", err.Error()))
                                continue</span>
                        }
                }

                <span class="cov8" title="1">if multipleDirs </span><span class="cov8" title="1">{
                        dirContents = append(dirContents, fmt.Sprintf("%v:", dirPath))
                }</span>

                <span class="cov8" title="1">dirContents = append(dirContents, files...)
                content = append(content, dirContents)
                dirContents = []string{}</span>
        }
        <span class="cov8" title="1">for _, err := range outErrors </span><span class="cov8" title="1">{
                fmt.Println(err)
        }</span>

        <span class="cov8" title="1">for i, file := range singleFiles </span><span class="cov8" title="1">{
                if flags.Longformat </span><span class="cov8" title="1">{
                        fmt.Println(file)
                        continue</span>
                }

                <span class="cov8" title="1">if i == len(singleFiles)-1 </span><span class="cov8" title="1">{
                        fmt.Print(file + "\n\n")
                        continue</span>
                }
                <span class="cov0" title="0">fmt.Print(file + "  ")</span>
        }

        <span class="cov8" title="1">for i, c := range content </span><span class="cov8" title="1">{
                if i != 0 </span><span class="cov8" title="1">{
                        fmt.Println()
                }</span>

                <span class="cov8" title="1">lines := c.([]string)
                if len(lines) == 0 </span><span class="cov0" title="0">{
                        continue</span>
                }

                // Print directory header (if present)
                <span class="cov8" title="1">if len(lines) &gt; 0 &amp;&amp; len(lines[0]) &gt; 0 &amp;&amp; lines[0][len(lines[0])-1] == ':' </span><span class="cov8" title="1">{
                        fmt.Println(lines[0])
                        lines = lines[1:] // Skip the header for content printing
                }</span>

                // Print the directory contents
                <span class="cov8" title="1">if flags.Longformat </span><span class="cov8" title="1">{
                        for _, line := range lines </span><span class="cov8" title="1">{
                                fmt.Println(line)
                        }</span>
                } else<span class="cov8" title="1"> {
                        // Use column formatting for short format
                        formatted := formatInColumns(lines)
                        fmt.Print(formatted)
                        if len(lines) &gt; 0 </span><span class="cov8" title="1">{
                                fmt.Println() // Add newline after the formatted output
                        }</span>
                }
        }
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package util

import "regexp"

var ansiPrefix = regexp.MustCompile(`^\x1b\[[0-9;]*m`)

func HasANSIPrefix(s string) bool <span class="cov8" title="1">{
        return ansiPrefix.MatchString(s)
}</span>
</pre>
		
		<pre class="file" id="file3" style="display: none">package util

import (
        "fmt"
        "os"
)

// IsValidDir ensures dirPath is a valid directory
func IsValidDir(dirPath string) (os.FileInfo, error) <span class="cov8" title="1">{
        info, err := os.Stat(dirPath)
        if err != nil </span><span class="cov8" title="1">{
                return nil, fmt.Errorf("cannot access '%v': No such file or directory", dirPath)
        }</span>

        <span class="cov8" title="1">return info, nil</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package util

import (
        "fmt"
        "os"
        "os/user"
        "sort"
        "strings"
        "syscall"
)

type Flags struct {
        ShowAll    bool
        Longformat bool
        Reverse    bool
        Recursive  bool
        TimeSort   bool
}

type fileDisplayInfo struct {
        os.FileInfo

        mode    string
        links   string
        user    string
        group   string
        size    string
        modTime string
}

type maxWidths struct {
        links int
        user  int
        group int
        size  int
}

const (
        reset = "\033[0m"

        dirColour     = "\033[01;34m"    // bold blue
        exeColour     = "\033[01;32m"    // bold green
        symlinkColour = "\033[01;36m"    // bold cyan
        socketColour  = "\033[01;35m"    // bold magenta
        pipeColour    = "\033[40;33m"    // yellow on black background
        deviceColour  = "\033[40;33;01m" // bold yellow on black (block/char dev)
        archiveColour = "\033[01;31m"    // bold red
)

// ReadDirNames returns a list of file and directory names in dirPath
func ReadDirNames(dirPath string, flag Flags) ([]string, error) <span class="cov8" title="1">{
        dir, err := os.Open(dirPath)
        if err != nil </span><span class="cov8" title="1">{
                return nil, err
        }</span>
        <span class="cov8" title="1">defer dir.Close()

        entries, err := dir.Readdir(-1)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov8" title="1">var names []string

        // Add . and .. entries when showAll is true
        if flag.ShowAll </span><span class="cov8" title="1">{
                names = append(names, fmt.Sprintf("%s.%s", dirColour, reset))
                names = append(names, fmt.Sprintf("%s..%s", dirColour, reset))
        }</span>

        <span class="cov8" title="1">for _, entry := range entries </span><span class="cov8" title="1">{
                name := entry.Name()

                // Skip hidden files unless showAll is true
                if !flag.ShowAll &amp;&amp; strings.HasPrefix(name, ".") </span><span class="cov8" title="1">{
                        continue</span>
                }

                <span class="cov8" title="1">mode := entry.Mode()
                colour := reset

                switch </span>{
                case mode.IsDir():<span class="cov8" title="1">
                        colour = dirColour</span>

                case mode&amp;os.ModeSymlink != 0:<span class="cov0" title="0">
                        colour = symlinkColour</span>

                case mode&amp;os.ModeSocket != 0:<span class="cov0" title="0">
                        colour = socketColour</span>

                case mode&amp;os.ModeNamedPipe != 0:<span class="cov0" title="0">
                        colour = pipeColour</span>

                case mode&amp;os.ModeDevice != 0:<span class="cov0" title="0">
                        colour = deviceColour</span>

                case mode&amp;0o111 != 0:<span class="cov0" title="0">
                        colour = exeColour</span>

                case strings.HasSuffix(name, ".tar") ||
                        strings.HasSuffix(name, ".gz") ||
                        strings.HasSuffix(name, ".tgz") ||
                        strings.HasSuffix(name, ".zip") ||
                        strings.HasSuffix(name, ".bz2") ||
                        strings.HasSuffix(name, ".xz"):<span class="cov8" title="1">
                        colour = archiveColour</span>
                }

                <span class="cov8" title="1">if flag.TimeSort </span><span class="cov8" title="1">{
                        names = InsertSortedByTime(name, colour, reset, dirPath, names)
                }</span> else<span class="cov8" title="1"> {
                        names = InsertSorted(name, colour, reset, names)
                }</span>
        }

        <span class="cov8" title="1">if flag.Reverse </span><span class="cov8" title="1">{
                Reverse(names)
        }</span>

        <span class="cov8" title="1">return names, nil</span>
}

func ReadDirNamesLong(dirPath string, flag Flags) ([]string, error) <span class="cov8" title="1">{
        dir, err := os.Open(dirPath)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov8" title="1">defer dir.Close()

        entries, err := dir.Readdir(-1)
        if err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>

        <span class="cov8" title="1">var displayInfos []fileDisplayInfo
        var widths maxWidths
        var totalBlocks int64

        // Combine initial filtering and data gathering into a single list
        filesToProcess := []os.FileInfo{}
        if flag.ShowAll </span><span class="cov8" title="1">{
                for _, special := range []string{".", ".."} </span><span class="cov8" title="1">{
                        info, err := os.Lstat(joinPath(dirPath, special))
                        if err == nil </span><span class="cov8" title="1">{
                                filesToProcess = append(filesToProcess, info)
                        }</span>
                }
        }
        <span class="cov8" title="1">for _, entry := range entries </span><span class="cov8" title="1">{
                if !flag.ShowAll &amp;&amp; strings.HasPrefix(entry.Name(), ".") </span><span class="cov8" title="1">{
                        continue</span>
                }
                <span class="cov8" title="1">filesToProcess = append(filesToProcess, entry)</span>
        }

        <span class="cov8" title="1">for _, info := range filesToProcess </span><span class="cov8" title="1">{
                fullPath := joinPath(dirPath, info.Name())
                stat := getStat(fullPath)
                totalBlocks += int64(stat.Blocks)

                // Owner and group
                uid := fmt.Sprint(stat.Uid)
                owner := uid
                if u, err := user.LookupId(uid); err == nil </span><span class="cov8" title="1">{
                        owner = u.Username
                }</span>
                <span class="cov8" title="1">gid := fmt.Sprint(stat.Gid)
                group := gid
                if g, err := user.LookupGroupId(gid); err == nil </span><span class="cov8" title="1">{
                        group = g.Name
                }</span>

                // Convert numeric fields to strings for length calculation
                <span class="cov8" title="1">linksStr := fmt.Sprint(stat.Nlink)
                sizeStr := fmt.Sprint(info.Size())

                // Update max widths
                if len(linksStr) &gt; widths.links </span><span class="cov8" title="1">{
                        widths.links = len(linksStr)
                }</span>
                <span class="cov8" title="1">if len(owner) &gt; widths.user </span><span class="cov8" title="1">{
                        widths.user = len(owner)
                }</span>
                <span class="cov8" title="1">if len(group) &gt; widths.group </span><span class="cov8" title="1">{
                        widths.group = len(group)
                }</span>
                <span class="cov8" title="1">if len(sizeStr) &gt; widths.size </span><span class="cov8" title="1">{
                        widths.size = len(sizeStr)
                }</span>

                // Store the processed info
                <span class="cov8" title="1">displayInfos = append(displayInfos, fileDisplayInfo{
                        FileInfo: info,
                        mode:     info.Mode().String(),
                        links:    linksStr,
                        user:     owner,
                        group:    group,
                        size:     sizeStr,
                        modTime:  info.ModTime().Format("Jan _2 15:04"),
                })</span>
        }

        // Sort displayInfos by time if TimeSort is enabled
        <span class="cov8" title="1">if flag.TimeSort </span><span class="cov8" title="1">{
                // Sort by modification time (newest first)
                for i := 0; i &lt; len(displayInfos)-1; i++ </span><span class="cov8" title="1">{
                        for j := i + 1; j &lt; len(displayInfos); j++ </span><span class="cov8" title="1">{
                                // Skip . and .. entries - they should stay at the beginning
                                if displayInfos[i].Name() == "." || displayInfos[i].Name() == ".." </span><span class="cov0" title="0">{
                                        continue</span>
                                }
                                <span class="cov8" title="1">if displayInfos[j].Name() == "." || displayInfos[j].Name() == ".." </span><span class="cov0" title="0">{
                                        continue</span>
                                }

                                // Compare modification times (newer first)
                                <span class="cov8" title="1">if displayInfos[j].ModTime().After(displayInfos[i].ModTime()) </span><span class="cov0" title="0">{
                                        displayInfos[i], displayInfos[j] = displayInfos[j], displayInfos[i]
                                }</span>
                        }
                }
        }

        <span class="cov8" title="1">if flag.Reverse </span><span class="cov8" title="1">{
                for i, j := 0, len(displayInfos)-1; i &lt; j; i, j = i+1, j-1 </span><span class="cov8" title="1">{
                        displayInfos[i], displayInfos[j] = displayInfos[j], displayInfos[i]
                }</span>
        }

        <span class="cov8" title="1">var lines []string

        for _, di := range displayInfos </span><span class="cov8" title="1">{
                color := getFileColor(di.Mode(), di.Name())
                fileName := fmt.Sprintf("%s%s%s", color, di.Name(), reset)

                // Use the calculated max widths to format the line perfectly
                line := fmt.Sprintf("%-10s %*s %-*s %-*s %*s %s %s",
                        di.mode,
                        widths.links, di.links,
                        widths.user, di.user,
                        widths.group, di.group,
                        widths.size, di.size,
                        di.modTime,
                        fileName,
                )
                if flag.TimeSort </span><span class="cov8" title="1">{
                        // When using time sort, we've already sorted the displayInfos array
                        // so we just append in order
                        lines = append(lines, line)
                }</span> else<span class="cov8" title="1"> {
                        lines = InsertSortedLong(line, lines)
                }</span>
        }
        <span class="cov8" title="1">lines = append([]string{fmt.Sprintf("total %d", totalBlocks/2)}, lines...)

        return lines, nil</span>
}

// getFileColor remains the same as it's a perfect helper function
func getFileColor(mode os.FileMode, name string) string <span class="cov8" title="1">{
        switch </span>{
        case mode.IsDir():<span class="cov8" title="1">
                return dirColour</span>
        case mode&amp;os.ModeSymlink != 0:<span class="cov8" title="1">
                return symlinkColour</span>
        case mode&amp;os.ModeSocket != 0:<span class="cov8" title="1">
                return socketColour</span>
        case mode&amp;os.ModeNamedPipe != 0:<span class="cov8" title="1">
                return pipeColour</span>
        case mode&amp;os.ModeDevice != 0:<span class="cov8" title="1">
                return deviceColour</span>
        case mode&amp;0o111 != 0:<span class="cov8" title="1">
                return exeColour</span>
        case strings.HasSuffix(name, ".tar"),
                strings.HasSuffix(name, ".gz"),
                strings.HasSuffix(name, ".tgz"),
                strings.HasSuffix(name, ".zip"),
                strings.HasSuffix(name, ".bz2"),
                strings.HasSuffix(name, ".xz"):<span class="cov8" title="1">
                return archiveColour</span>
        default:<span class="cov8" title="1">
                return reset</span>
        }
}

// getStat also remains the same
func getStat(path string) syscall.Stat_t <span class="cov8" title="1">{
        var stat syscall.Stat_t
        if err := syscall.Lstat(path, &amp;stat); err != nil </span><span class="cov8" title="1">{
                _ = syscall.Stat(path, &amp;stat)
        }</span>
        <span class="cov8" title="1">return stat</span>
}

// joinPath joins directory and file name with proper separator
func joinPath(dir, file string) string <span class="cov8" title="1">{
        if dir == "" </span><span class="cov8" title="1">{
                return file
        }</span>
        <span class="cov8" title="1">if strings.HasSuffix(dir, "/") </span><span class="cov8" title="1">{
                return dir + file
        }</span>
        <span class="cov8" title="1">return dir + "/" + file</span>
}

// getAbsPath returns absolute path (simplified version)
func getAbsPath(path string) (string, error) <span class="cov8" title="1">{
        if strings.HasPrefix(path, "/") </span><span class="cov8" title="1">{
                return path, nil
        }</span>
        // For relative paths, we'll use a simple approach
        // In a real implementation, you'd need to get the current working directory
        // but since we can't use filepath, we'll return the path as-is for relative paths
        <span class="cov8" title="1">return path, nil</span>
}

// CollectDirectoriesRecursively traverses directories recursively and returns all directory paths
func CollectDirectoriesRecursively(rootPaths []string, flags Flags) ([]string, error) <span class="cov8" title="1">{
        var allDirs []string
        visited := make(map[string]bool)

        for _, rootPath := range rootPaths </span><span class="cov8" title="1">{
                info, err := IsValidDir(rootPath)
                if err != nil </span><span class="cov8" title="1">{
                        return nil, err
                }</span>

                <span class="cov8" title="1">if !info.IsDir() </span><span class="cov8" title="1">{
                        // If it's a file, just add it to the list
                        allDirs = append(allDirs, rootPath)
                        continue</span>
                }

                // Add the root directory first
                <span class="cov8" title="1">allDirs = append(allDirs, rootPath)

                // Recursively collect subdirectories
                err = collectSubdirectories(rootPath, flags, &amp;allDirs, visited)
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
        }

        <span class="cov8" title="1">return allDirs, nil</span>
}

// collectSubdirectories is a helper function that recursively collects subdirectories
func collectSubdirectories(dirPath string, flags Flags, allDirs *[]string, visited map[string]bool) error <span class="cov8" title="1">{
        // Prevent infinite loops with symlinks
        absPath, err := getAbsPath(dirPath)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov8" title="1">if visited[absPath] </span><span class="cov0" title="0">{
                return nil
        }</span>
        <span class="cov8" title="1">visited[absPath] = true

        dir, err := os.Open(dirPath)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>
        <span class="cov8" title="1">defer dir.Close()

        entries, err := dir.Readdir(-1)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        // Collect directories first
        <span class="cov8" title="1">var subdirs []string
        for _, entry := range entries </span><span class="cov8" title="1">{
                name := entry.Name()

                // Skip hidden files unless showAll is true
                if !flags.ShowAll &amp;&amp; strings.HasPrefix(name, ".") </span><span class="cov8" title="1">{
                        continue</span>
                }

                // Skip . and .. entries
                <span class="cov8" title="1">if name == "." || name == ".." </span><span class="cov0" title="0">{
                        continue</span>
                }

                <span class="cov8" title="1">if entry.IsDir() </span><span class="cov8" title="1">{
                        subdirs = append(subdirs, name)
                }</span>
        }

        // Sort directories using CompareStrings
        <span class="cov8" title="1">sort.Slice(subdirs, func(i, j int) bool </span><span class="cov8" title="1">{
                return CompareStrings(subdirs[i], subdirs[j])
        }</span>)

        // Process sorted directories
        <span class="cov8" title="1">for _, name := range subdirs </span><span class="cov8" title="1">{
                subDirPath := joinPath(dirPath, name)
                *allDirs = append(*allDirs, subDirPath)

                // Recursively process subdirectory
                err = collectSubdirectories(subDirPath, flags, allDirs, visited)
                if err != nil </span><span class="cov0" title="0">{
                        // Continue processing other directories even if one fails
                        continue</span>
                }
        }

        <span class="cov8" title="1">return nil</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package util

func Reverse[T any](s []T) <span class="cov8" title="1">{
        for i, j := 0, len(s)-1; i &lt; j; i, j = i+1, j-1 </span><span class="cov8" title="1">{
                s[i], s[j] = s[j], s[i]
        }</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package util

import (
        "fmt"
        "os"
        "strings"
)

// CompareStrings compares two strings based on custom sorting rules.
// Significant parts (numeric and alphabetic characters) are compared first, with numeric characters before alphabetic.
// Numeric characters (0-9) are compared by ASCII values, alphabetic characters (a-z, A-Z) case-insensitively with lowercase prioritized.
// If significant parts are equal, compare original strings character by character:
// - Numeric vs. anything: numeric comes first.
// - Alphabetic vs. alphabetic: lowercase before uppercase, case-insensitive otherwise.
// - Other pairs (special vs. special, alphabetic vs. special): ASCII order.
func CompareStrings(a, b string) bool <span class="cov8" title="1">{
        // Convert strings to runes for proper handling
        ra, rb := []rune(a), []rune(b)

        // Check if both strings contain only special (non-alphabetic, non-numeric) characters
        hasSignificantA, hasSignificantB := false, false
        for _, r := range ra </span><span class="cov8" title="1">{
                if (r &gt;= 'a' &amp;&amp; r &lt;= 'z') || (r &gt;= 'A' &amp;&amp; r &lt;= 'Z') || (r &gt;= '0' &amp;&amp; r &lt;= '9') </span><span class="cov8" title="1">{
                        hasSignificantA = true
                        break</span>
                }
        }
        <span class="cov8" title="1">for _, r := range rb </span><span class="cov8" title="1">{
                if (r &gt;= 'a' &amp;&amp; r &lt;= 'z') || (r &gt;= 'A' &amp;&amp; r &lt;= 'Z') || (r &gt;= '0' &amp;&amp; r &lt;= '9') </span><span class="cov8" title="1">{
                        hasSignificantB = true
                        break</span>
                }
        }

        // If both strings have only special characters (no letters or digits), compare by ASCII values
        <span class="cov8" title="1">if !hasSignificantA &amp;&amp; !hasSignificantB </span><span class="cov8" title="1">{
                for i := 0; i &lt; len(ra) &amp;&amp; i &lt; len(rb); i++ </span><span class="cov8" title="1">{
                        if ra[i] != rb[i] </span><span class="cov8" title="1">{
                                return ra[i] &lt; rb[i]
                        }</span>
                }
                // If equal up to shorter length, shorter string comes first
                <span class="cov8" title="1">return len(ra) &lt; len(rb)</span>
        }

        // If either string has significant characters (letters or digits), compare significant parts first
        <span class="cov8" title="1">var sigA, sigB []rune
        for _, r := range ra </span><span class="cov8" title="1">{
                if (r &gt;= 'a' &amp;&amp; r &lt;= 'z') || (r &gt;= 'A' &amp;&amp; r &lt;= 'Z') </span><span class="cov8" title="1">{
                        sigA = append(sigA, []rune(strings.ToLower(string(r)))[0])
                }</span> else<span class="cov8" title="1"> if r &gt;= '0' &amp;&amp; r &lt;= '9' </span><span class="cov8" title="1">{
                        sigA = append(sigA, r)
                }</span>
        }
        <span class="cov8" title="1">for _, r := range rb </span><span class="cov8" title="1">{
                if (r &gt;= 'a' &amp;&amp; r &lt;= 'z') || (r &gt;= 'A' &amp;&amp; r &lt;= 'Z') </span><span class="cov8" title="1">{
                        sigB = append(sigB, []rune(strings.ToLower(string(r)))[0])
                }</span> else<span class="cov8" title="1"> if r &gt;= '0' &amp;&amp; r &lt;= '9' </span><span class="cov8" title="1">{
                        sigB = append(sigB, r)
                }</span>
        }

        // Compare significant parts (numeric before alphabetic, alphabetic case-insensitive)
        <span class="cov8" title="1">for i := 0; i &lt; len(sigA) &amp;&amp; i &lt; len(sigB); i++ </span><span class="cov8" title="1">{
                ca, cb := sigA[i], sigB[i]
                if ca == cb </span><span class="cov8" title="1">{
                        continue</span>
                }
                // If one is numeric and the other alphabetic, numeric comes first
                <span class="cov8" title="1">if (ca &gt;= '0' &amp;&amp; ca &lt;= '9') &amp;&amp; ((cb &gt;= 'a' &amp;&amp; cb &lt;= 'z') || (cb &gt;= 'A' &amp;&amp; cb &lt;= 'Z')) </span><span class="cov8" title="1">{
                        return true
                }</span>
                <span class="cov8" title="1">if ((ca &gt;= 'a' &amp;&amp; ca &lt;= 'z') || (ca &gt;= 'A' &amp;&amp; ca &lt;= 'Z')) &amp;&amp; (cb &gt;= '0' &amp;&amp; cb &lt;= '9') </span><span class="cov8" title="1">{
                        return false
                }</span>
                // If both are alphabetic or both numeric, compare directly
                <span class="cov8" title="1">return ca &lt; cb</span>
        }
        // If significant parts are equal, shorter significant part comes first
        <span class="cov8" title="1">if len(sigA) != len(sigB) </span><span class="cov8" title="1">{
                return len(sigA) &lt; len(sigB)
        }</span>

        // If significant parts are equal, compare original strings character by character
        <span class="cov8" title="1">for i := 0; i &lt; len(ra) &amp;&amp; i &lt; len(rb); i++ </span><span class="cov8" title="1">{
                ca, cb := ra[i], rb[i]
                if ca == cb </span><span class="cov8" title="1">{
                        continue</span>
                }
                // Prioritize numeric characters over all others
                <span class="cov8" title="1">if (ca &gt;= '0' &amp;&amp; ca &lt;= '9') &amp;&amp; !(cb &gt;= '0' &amp;&amp; cb &lt;= '9') </span><span class="cov0" title="0">{
                        return true
                }</span>
                <span class="cov8" title="1">if !(ca &gt;= '0' &amp;&amp; ca &lt;= '9') &amp;&amp; (cb &gt;= '0' &amp;&amp; cb &lt;= '9') </span><span class="cov0" title="0">{
                        return false
                }</span>
                <span class="cov8" title="1">if (ca &gt;= '0' &amp;&amp; ca &lt;= '9') &amp;&amp; (cb &gt;= '0' &amp;&amp; cb &lt;= '9') </span><span class="cov0" title="0">{
                        return ca &lt; cb
                }</span>
                // If both are alphabetic, prioritize lowercase
                <span class="cov8" title="1">if ((ca &gt;= 'a' &amp;&amp; ca &lt;= 'z') || (ca &gt;= 'A' &amp;&amp; ca &lt;= 'Z')) &amp;&amp; ((cb &gt;= 'a' &amp;&amp; cb &lt;= 'z') || (cb &gt;= 'A' &amp;&amp; cb &lt;= 'Z')) </span><span class="cov8" title="1">{
                        caLower := []rune(strings.ToLower(string(ca)))[0]
                        cbLower := []rune(strings.ToLower(string(cb)))[0]
                        if caLower != cbLower </span><span class="cov0" title="0">{
                                return caLower &lt; cbLower
                        }</span>
                        // If lowercase versions are equal, lowercase comes before uppercase
                        <span class="cov8" title="1">if (ca &gt;= 'a' &amp;&amp; ca &lt;= 'z') &amp;&amp; (cb &gt;= 'A' &amp;&amp; cb &lt;= 'Z') </span><span class="cov8" title="1">{
                                return true
                        }</span>
                        <span class="cov8" title="1">if (ca &gt;= 'A' &amp;&amp; ca &lt;= 'Z') &amp;&amp; (cb &gt;= 'a' &amp;&amp; cb &lt;= 'z') </span><span class="cov8" title="1">{
                                return false
                        }</span>
                        <span class="cov0" title="0">continue</span>
                }
                // For any other comparison (special vs. special or alphabetic vs. special), use ASCII
                <span class="cov8" title="1">return ca &lt; cb</span>
        }
        // If equal up to shorter length, shorter string comes first
        <span class="cov8" title="1">return len(ra) &lt; len(rb)</span>
}

func InsertSorted(name, colour, reset string, names []string) []string <span class="cov8" title="1">{
        if name == "." || name == ".." </span><span class="cov8" title="1">{
                return append([]string{fmt.Sprintf("%s%s%s", colour, name, reset)}, names...)
        }</span>
        <span class="cov8" title="1">colored := fmt.Sprintf("%s%s%s", colour, name, reset)

        for i, val := range names </span><span class="cov8" title="1">{
                cleanVal := TrimStart(StripANSI(val))
                if (CompareStrings(TrimStart(name), cleanVal) || (TrimStart(name) == cleanVal)) &amp;&amp; (val != "." &amp;&amp; val != "..") </span><span class="cov8" title="1">{
                        return append(names[:i], append([]string{colored}, names[i:]...)...)
                }</span>
        }

        <span class="cov8" title="1">return append(names, colored)</span>
}

func InsertSortedLong(line string, lines []string) []string <span class="cov8" title="1">{
        for i, val := range lines </span><span class="cov8" title="1">{
                lineName := TrimStart(StripANSI(StripLong(line)))
                valName := TrimStart(StripANSI(StripLong(val)))
                if CompareStrings(lineName, valName) || (lineName == valName) </span><span class="cov8" title="1">{
                        return append(lines[:i], append([]string{line}, lines[i:]...)...)
                }</span>
        }

        <span class="cov8" title="1">return append(lines, line)</span>
}

func TrimStart(name string) string <span class="cov8" title="1">{
        return strings.TrimLeft(name, ".")
}</span>

// InsertSortedByTime inserts a file into a list sorted by modification time (newest first)
func InsertSortedByTime(name, colour, reset, dirPath string, names []string) []string <span class="cov8" title="1">{
        if name == "." || name == ".." </span><span class="cov8" title="1">{
                return append([]string{fmt.Sprintf("%s%s%s", colour, name, reset)}, names...)
        }</span>
        <span class="cov8" title="1">colored := fmt.Sprintf("%s%s%s", colour, name, reset)

        // Get modification time for the new file
        newFilePath := joinPath(dirPath, name)
        newInfo, err := os.Stat(newFilePath)
        if err != nil </span><span class="cov8" title="1">{
                // If we can't get the time, fall back to alphabetical sorting
                return InsertSorted(name, colour, reset, names)
        }</span>
        <span class="cov8" title="1">newTime := newInfo.ModTime()

        for i, val := range names </span><span class="cov8" title="1">{
                cleanVal := StripANSI(val)
                if cleanVal == "." || cleanVal == ".." </span><span class="cov8" title="1">{
                        continue</span>
                }

                // Get modification time for the existing file
                <span class="cov8" title="1">existingFilePath := joinPath(dirPath, cleanVal)
                existingInfo, err := os.Stat(existingFilePath)
                if err != nil </span><span class="cov0" title="0">{
                        // If we can't get the time, insert here
                        return append(names[:i], append([]string{colored}, names[i:]...)...)
                }</span>
                <span class="cov8" title="1">existingTime := existingInfo.ModTime()

                // Insert if new file is newer (or same time, then alphabetical)
                if newTime.After(existingTime) || (newTime.Equal(existingTime) &amp;&amp; CompareStrings(name, cleanVal)) </span><span class="cov8" title="1">{
                        return append(names[:i], append([]string{colored}, names[i:]...)...)
                }</span>
        }

        <span class="cov8" title="1">return append(names, colored)</span>
}

// InsertSortedLongByTime inserts a long format line sorted by modification time (newest first)
func InsertSortedLongByTime(line, dirPath string, lines []string) []string <span class="cov8" title="1">{
        fileName := StripANSI(StripLong(line))

        // Get modification time for the new file
        newFilePath := joinPath(dirPath, fileName)
        newInfo, err := os.Stat(newFilePath)
        if err != nil </span><span class="cov8" title="1">{
                // If we can't get the time, fall back to alphabetical sorting
                return InsertSortedLong(line, lines)
        }</span>
        <span class="cov8" title="1">newTime := newInfo.ModTime()

        for i, val := range lines </span><span class="cov8" title="1">{
                existingFileName := StripANSI(StripLong(val))

                // Get modification time for the existing file
                existingFilePath := joinPath(dirPath, existingFileName)
                existingInfo, err := os.Stat(existingFilePath)
                if err != nil </span><span class="cov0" title="0">{
                        // If we can't get the time, insert here
                        return append(lines[:i], append([]string{line}, lines[i:]...)...)
                }</span>
                <span class="cov8" title="1">existingTime := existingInfo.ModTime()

                // Insert if new file is newer (or same time, then alphabetical)
                if newTime.After(existingTime) || (newTime.Equal(existingTime) &amp;&amp; CompareStrings(fileName, existingFileName)) </span><span class="cov8" title="1">{
                        return append(lines[:i], append([]string{line}, lines[i:]...)...)
                }</span>
        }

        <span class="cov0" title="0">return append(lines, line)</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package util

import "regexp"

var ansiEscape = regexp.MustCompile(`\x1b\[[0-9;]*m`)
var longFormat = regexp.MustCompile(`^([\-d][rwx\-]{9})\s+\d+\s+\S+\s+\S+\s+\d+\s+[A-Z][a-z]{2}\s+\d{1,2}\s+\d{2}:\d{2}\s+`)

// StripAnsi removes ANSI escape codes from a string
func StripANSI(s string) string <span class="cov8" title="1">{
        return ansiEscape.ReplaceAllString(s, "")
}</span>

func StripLong(s string) string <span class="cov8" title="1">{
        return longFormat.ReplaceAllString(s, "")
}</span>
</pre>
		
		<pre class="file" id="file8" style="display: none">package util

import "time"

func FormatTime(t time.Time) string <span class="cov8" title="1">{
        return t.Format("Jan _2 15:04")
}</span>
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
